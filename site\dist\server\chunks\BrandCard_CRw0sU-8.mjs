import { jsxs, jsx } from 'react/jsx-runtime';
import { C as Card, b as <PERSON><PERSON><PERSON><PERSON>, c as <PERSON><PERSON><PERSON><PERSON>, d as CardDescription, a as CardContent } from './card_DVAsqpYZ.mjs';
import { B as Badge } from './badge_B5LLCYzT.mjs';
import { n as navigate } from './router_vN4ZPF0m.mjs';

function BrandCard({ brand, onClick }) {
  const handleClick = () => {
    if (onClick) {
      onClick();
    } else {
      navigate(`/catalog/brands/${brand.slug}`);
    }
  };
  return /* @__PURE__ */ jsxs(
    Card,
    {
      className: "cursor-pointer transition-all hover:shadow-md hover:scale-[1.02]",
      onClick: handleClick,
      children: [
        /* @__PURE__ */ jsx(CardHeader, { className: "pb-3", children: /* @__PURE__ */ jsxs("div", { className: "flex items-start justify-between", children: [
          /* @__PURE__ */ jsxs("div", { className: "flex-1", children: [
            /* @__PURE__ */ jsx(CardTitle, { className: "text-lg line-clamp-1", children: brand.name }),
            brand.country && /* @__PURE__ */ jsx(CardDescription, { className: "mt-1", children: brand.country })
          ] }),
          /* @__PURE__ */ jsx("div", { className: "flex flex-col gap-1", children: /* @__PURE__ */ jsx(Badge, { variant: brand.isOem ? "default" : "secondary", children: brand.isOem ? "OEM" : "Aftermarket" }) })
        ] }) }),
        /* @__PURE__ */ jsx(CardContent, { className: "pt-0", children: brand._count && /* @__PURE__ */ jsx("div", { className: "space-y-2", children: /* @__PURE__ */ jsxs("div", { className: "grid grid-cols-2 gap-2 text-sm", children: [
          /* @__PURE__ */ jsxs("div", { className: "flex justify-between", children: [
            /* @__PURE__ */ jsx("span", { className: "text-muted-foreground", children: "Запчасти:" }),
            /* @__PURE__ */ jsx("span", { className: "font-medium", children: brand._count.catalogItems })
          ] }),
          /* @__PURE__ */ jsxs("div", { className: "flex justify-between", children: [
            /* @__PURE__ */ jsx("span", { className: "text-muted-foreground", children: "Модели:" }),
            /* @__PURE__ */ jsx("span", { className: "font-medium", children: brand._count.equipmentModel })
          ] })
        ] }) }) })
      ]
    }
  );
}

export { BrandCard as B };
