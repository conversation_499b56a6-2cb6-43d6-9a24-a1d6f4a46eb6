import{j as e}from"./jsx-runtime.D_zvdyIk.js";import{r as l}from"./index.0yr9KlQE.js";import{u as ke,C as X,D as U,a as $,b as ee,c as se}from"./dialog.BXJ4m5WQ.js";import{c as te,P as D,a as y,u as ae,b as Fe,M as A,f as R,g as E,e as _}from"./index.Di12BYGB.js";import{c as Se,u as re}from"./index.k1eM7uTc.js";import{u as Ae}from"./index.BWNh2K4G.js";import{u as ne,X as Re}from"./index.NogD4Y5M.js";import{c as k}from"./utils.CBfrqCZ4.js";import{B as T}from"./badge.CrhVNNKw.js";import{S as Z}from"./status-badge.BsGsEBtY.js";import{M as w,T as Ee}from"./TrpcBoundary.D5wdV5Rx.js";import{I as _e,F as L}from"./image.BtzVA0_o.js";import{c as oe}from"./createLucideIcon.DdiNmGRb.js";import{D as J}from"./download.D1J5CNLA.js";import"./index.ViApDAiE.js";import"./index.3rXK4OIH.js";import"./clients.CgSQcUWs.js";/**
 * @license lucide-react v0.540.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const De=[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]],Q=oe("chevron-left",De);/**
 * @license lucide-react v0.540.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Me=[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["line",{x1:"21",x2:"16.65",y1:"21",y2:"16.65",key:"13gj7c"}],["line",{x1:"11",x2:"11",y1:"8",y2:"14",key:"1vmskp"}],["line",{x1:"8",x2:"14",y1:"11",y2:"11",key:"durymu"}]],Pe=oe("zoom-in",Me);var K="rovingFocusGroup.onEntryFocus",ze={bubbles:!1,cancelable:!0},z="RovingFocusGroup",[H,ie,Oe]=Se(z),[Le,le]=te(z,[Oe]),[Ge,Be]=Le(z),ce=l.forwardRef((s,a)=>e.jsx(H.Provider,{scope:s.__scopeRovingFocusGroup,children:e.jsx(H.Slot,{scope:s.__scopeRovingFocusGroup,children:e.jsx(Ke,{...s,ref:a})})}));ce.displayName=z;var Ke=l.forwardRef((s,a)=>{const{__scopeRovingFocusGroup:o,orientation:t,loop:i=!1,dir:x,currentTabStopId:d,defaultCurrentTabStopId:f,onCurrentTabStopIdChange:j,onEntryFocus:p,preventScrollOnEntryFocus:r=!1,...m}=s,c=l.useRef(null),h=Ae(a,c),I=re(x),[F,u]=ae({prop:d,defaultProp:f??null,onChange:j,caller:z}),[b,S]=l.useState(!1),g=ke(p),n=ie(o),N=l.useRef(!1),[O,Y]=l.useState(0);return l.useEffect(()=>{const v=c.current;if(v)return v.addEventListener(K,g),()=>v.removeEventListener(K,g)},[g]),e.jsx(Ge,{scope:o,orientation:t,dir:I,loop:i,currentTabStopId:F,onItemFocus:l.useCallback(v=>u(v),[u]),onItemShiftTab:l.useCallback(()=>S(!0),[]),onFocusableItemAdd:l.useCallback(()=>Y(v=>v+1),[]),onFocusableItemRemove:l.useCallback(()=>Y(v=>v-1),[]),children:e.jsx(D.div,{tabIndex:b||O===0?-1:0,"data-orientation":t,...m,ref:h,style:{outline:"none",...s.style},onMouseDown:y(s.onMouseDown,()=>{N.current=!0}),onFocus:y(s.onFocus,v=>{const ye=!N.current;if(v.target===v.currentTarget&&ye&&!b){const q=new CustomEvent(K,ze);if(v.currentTarget.dispatchEvent(q),!q.defaultPrevented){const B=n().filter(C=>C.focusable),Ie=B.find(C=>C.active),Ce=B.find(C=>C.id===F),Te=[Ie,Ce,...B].filter(Boolean).map(C=>C.ref.current);ue(Te,r)}}N.current=!1}),onBlur:y(s.onBlur,()=>S(!1))})})}),de="RovingFocusGroupItem",me=l.forwardRef((s,a)=>{const{__scopeRovingFocusGroup:o,focusable:t=!0,active:i=!1,tabStopId:x,children:d,...f}=s,j=ne(),p=x||j,r=Be(de,o),m=r.currentTabStopId===p,c=ie(o),{onFocusableItemAdd:h,onFocusableItemRemove:I,currentTabStopId:F}=r;return l.useEffect(()=>{if(t)return h(),()=>I()},[t,h,I]),e.jsx(H.ItemSlot,{scope:o,id:p,focusable:t,active:i,children:e.jsx(D.span,{tabIndex:m?0:-1,"data-orientation":r.orientation,...f,ref:a,onMouseDown:y(s.onMouseDown,u=>{t?r.onItemFocus(p):u.preventDefault()}),onFocus:y(s.onFocus,()=>r.onItemFocus(p)),onKeyDown:y(s.onKeyDown,u=>{if(u.key==="Tab"&&u.shiftKey){r.onItemShiftTab();return}if(u.target!==u.currentTarget)return;const b=$e(u,r.orientation,r.dir);if(b!==void 0){if(u.metaKey||u.ctrlKey||u.altKey||u.shiftKey)return;u.preventDefault();let g=c().filter(n=>n.focusable).map(n=>n.ref.current);if(b==="last")g.reverse();else if(b==="prev"||b==="next"){b==="prev"&&g.reverse();const n=g.indexOf(u.currentTarget);g=r.loop?He(g,n+1):g.slice(n+1)}setTimeout(()=>ue(g))}}),children:typeof d=="function"?d({isCurrentTabStop:m,hasTabStop:F!=null}):d})})});me.displayName=de;var Ve={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function Ue(s,a){return a!=="rtl"?s:s==="ArrowLeft"?"ArrowRight":s==="ArrowRight"?"ArrowLeft":s}function $e(s,a,o){const t=Ue(s.key,o);if(!(a==="vertical"&&["ArrowLeft","ArrowRight"].includes(t))&&!(a==="horizontal"&&["ArrowUp","ArrowDown"].includes(t)))return Ve[t]}function ue(s,a=!1){const o=document.activeElement;for(const t of s)if(t===o||(t.focus({preventScroll:a}),document.activeElement!==o))return}function He(s,a){return s.map((o,t)=>s[(a+t)%s.length])}var We=ce,Ye=me,G="Tabs",[qe,ws]=te(G,[le]),xe=le(),[Xe,W]=qe(G),he=l.forwardRef((s,a)=>{const{__scopeTabs:o,value:t,onValueChange:i,defaultValue:x,orientation:d="horizontal",dir:f,activationMode:j="automatic",...p}=s,r=re(f),[m,c]=ae({prop:t,onChange:i,defaultProp:x??"",caller:G});return e.jsx(Xe,{scope:o,baseId:ne(),value:m,onValueChange:c,orientation:d,dir:r,activationMode:j,children:e.jsx(D.div,{dir:r,"data-orientation":d,...p,ref:a})})});he.displayName=G;var fe="TabsList",pe=l.forwardRef((s,a)=>{const{__scopeTabs:o,loop:t=!0,...i}=s,x=W(fe,o),d=xe(o);return e.jsx(We,{asChild:!0,...d,orientation:x.orientation,dir:x.dir,loop:t,children:e.jsx(D.div,{role:"tablist","aria-orientation":x.orientation,...i,ref:a})})});pe.displayName=fe;var ge="TabsTrigger",ve=l.forwardRef((s,a)=>{const{__scopeTabs:o,value:t,disabled:i=!1,...x}=s,d=W(ge,o),f=xe(o),j=Ne(d.baseId,t),p=we(d.baseId,t),r=t===d.value;return e.jsx(Ye,{asChild:!0,...f,focusable:!i,active:r,children:e.jsx(D.button,{type:"button",role:"tab","aria-selected":r,"aria-controls":p,"data-state":r?"active":"inactive","data-disabled":i?"":void 0,disabled:i,id:j,...x,ref:a,onMouseDown:y(s.onMouseDown,m=>{!i&&m.button===0&&m.ctrlKey===!1?d.onValueChange(t):m.preventDefault()}),onKeyDown:y(s.onKeyDown,m=>{[" ","Enter"].includes(m.key)&&d.onValueChange(t)}),onFocus:y(s.onFocus,()=>{const m=d.activationMode!=="manual";!r&&!i&&m&&d.onValueChange(t)})})})});ve.displayName=ge;var be="TabsContent",je=l.forwardRef((s,a)=>{const{__scopeTabs:o,value:t,forceMount:i,children:x,...d}=s,f=W(be,o),j=Ne(f.baseId,t),p=we(f.baseId,t),r=t===f.value,m=l.useRef(r);return l.useEffect(()=>{const c=requestAnimationFrame(()=>m.current=!1);return()=>cancelAnimationFrame(c)},[]),e.jsx(Fe,{present:i||r,children:({present:c})=>e.jsx(D.div,{"data-state":r?"active":"inactive","data-orientation":f.orientation,role:"tabpanel","aria-labelledby":j,hidden:!c,id:p,tabIndex:0,...d,ref:a,style:{...s.style,animationDuration:m.current?"0s":void 0},children:c&&x})})});je.displayName=be;function Ne(s,a){return`${s}-trigger-${a}`}function we(s,a){return`${s}-content-${a}`}var Ze=he,Je=pe,Qe=ve,es=je;function ss({className:s,...a}){return e.jsx(Ze,{"data-slot":"tabs",className:k("flex flex-col gap-2",s),...a})}function ts({className:s,...a}){return e.jsx(Je,{"data-slot":"tabs-list",className:k("bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]",s),...a})}function M({className:s,...a}){return e.jsx(Qe,{"data-slot":"tabs-trigger",className:k("data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",s),...a})}function P({className:s,...a}){return e.jsx(es,{"data-slot":"tabs-content",className:k("flex-1 outline-none",s),...a})}function V({mediaAssets:s,primaryImage:a,title:o,className:t}){const[i,x]=l.useState(0),[d,f]=l.useState(!1),[j,p]=l.useState(!1),[r,m]=l.useState(null),c=[...a?[a]:[],...s.filter(n=>n.id!==a?.id)];if(c.length===0)return e.jsx("div",{className:k("flex items-center justify-center h-48 bg-muted/30 rounded-lg border-2 border-dashed border-border",t),children:e.jsxs("div",{className:"text-center",children:[e.jsx(_e,{className:"h-12 w-12 text-muted-foreground mx-auto mb-2"}),e.jsx("p",{className:"text-sm text-muted-foreground",children:"Изображения отсутствуют"})]})});const h=c[i],I=h?.mimeType.startsWith("image/"),F=h?.mimeType==="application/pdf",u=n=>{if(!n)return"";const N=["B","KB","MB","GB"],O=Math.floor(Math.log(n)/Math.log(1024));return`${Math.round(n/Math.pow(1024,O)*100)/100} ${N[O]}`},b=(n,N)=>{x(N),m(n),n.mimeType.startsWith("image/")?f(!0):n.mimeType==="application/pdf"&&p(!0)},S=()=>x(n=>(n+1)%c.length),g=()=>x(n=>(n-1+c.length)%c.length);return e.jsxs("div",{className:k("space-y-4",t),children:[e.jsxs("div",{className:"relative group",children:[e.jsx("div",{className:"aspect-video bg-muted/30 rounded-lg overflow-hidden border-2 border-border-strong",children:I?e.jsx("img",{src:h.url||"/placeholder.svg",alt:h.fileName,className:"w-full h-full object-contain cursor-zoom-in hover:scale-105 transition-transform duration-300",onClick:()=>b(h,i),loading:"lazy"}):F?e.jsx("div",{className:"w-full h-full flex items-center justify-center cursor-pointer hover:bg-muted/50 transition-colors",onClick:()=>b(h,i),children:e.jsxs("div",{className:"text-center",children:[e.jsx(L,{className:"h-16 w-16 text-primary mx-auto mb-4"}),e.jsx("h3",{className:"font-semibold mb-2",children:h.fileName}),e.jsx(T,{variant:"outline",className:"mb-2",children:"PDF"}),h.fileSize&&e.jsx("p",{className:"text-sm text-muted-foreground",children:u(h.fileSize)})]})}):e.jsx("div",{className:"w-full h-full flex items-center justify-center",children:e.jsxs("div",{className:"text-center",children:[e.jsx(L,{className:"h-16 w-16 text-muted-foreground mx-auto mb-4"}),e.jsx("p",{className:"text-sm text-muted-foreground",children:"Неподдерживаемый формат"})]})})}),c.length>1&&e.jsxs(e.Fragment,{children:[e.jsx(w,{variant:"ghost",size:"icon",className:"absolute left-2 top-1/2 -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity bg-background/80 backdrop-blur-sm",onClick:g,children:e.jsx(Q,{className:"h-4 w-4"})}),e.jsx(w,{variant:"ghost",size:"icon",className:"absolute right-2 top-1/2 -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity bg-background/80 backdrop-blur-sm",onClick:S,children:e.jsx(X,{className:"h-4 w-4"})})]}),e.jsxs("div",{className:"absolute top-2 right-2 flex gap-2 opacity-0 group-hover:opacity-100 transition-opacity",children:[I&&e.jsx(w,{variant:"ghost",size:"icon",className:"bg-background/80 backdrop-blur-sm",onClick:()=>b(h,i),children:e.jsx(Pe,{className:"h-4 w-4"})}),e.jsx(w,{variant:"ghost",size:"icon",className:"bg-background/80 backdrop-blur-sm",onClick:()=>window.open(h.url,"_blank"),children:e.jsx(J,{className:"h-4 w-4"})})]}),e.jsxs("div",{className:"absolute bottom-2 left-2 flex gap-2",children:[e.jsxs(T,{variant:"secondary",className:"bg-background/80 backdrop-blur-sm",children:[i+1," / ",c.length]}),e.jsx(T,{variant:"outline",className:"bg-background/80 backdrop-blur-sm",children:h.mimeType.split("/")[1].toUpperCase()})]})]}),c.length>1&&e.jsx("div",{className:"flex gap-2 overflow-x-auto pb-2",children:c.map((n,N)=>e.jsx("button",{onClick:()=>x(N),className:k("flex-shrink-0 w-16 h-16 rounded-lg overflow-hidden border-2 transition-all",i===N?"border-primary shadow-md":"border-border hover:border-border-strong"),children:n.mimeType.startsWith("image/")?e.jsx("img",{src:n.url||"/placeholder.svg",alt:n.fileName,className:"w-full h-full object-cover",loading:"lazy"}):e.jsx("div",{className:"w-full h-full flex items-center justify-center bg-muted/50",children:e.jsx(L,{className:"h-6 w-6 text-muted-foreground"})})},n.id))}),e.jsx(U,{open:d,onOpenChange:f,children:e.jsx($,{className:"max-w-7xl max-h-[95vh] p-0 bg-black/95",children:e.jsxs("div",{className:"relative w-full h-[90vh] flex items-center justify-center",children:[e.jsx("img",{src:r?.url||"/placeholder.svg",alt:r?.fileName||"image",className:"max-w-full max-h-full object-contain"}),e.jsx(w,{variant:"ghost",size:"icon",className:"absolute top-4 right-4 text-white hover:bg-white/20",onClick:()=>f(!1),children:e.jsx(Re,{className:"h-6 w-6"})}),c.length>1&&e.jsxs(e.Fragment,{children:[e.jsx(w,{variant:"ghost",size:"icon",className:"absolute left-4 top-1/2 -translate-y-1/2 text-white hover:bg-white/20",onClick:g,children:e.jsx(Q,{className:"h-6 w-6"})}),e.jsx(w,{variant:"ghost",size:"icon",className:"absolute right-4 top-1/2 -translate-y-1/2 text-white hover:bg-white/20",onClick:S,children:e.jsx(X,{className:"h-6 w-6"})})]}),e.jsxs("div",{className:"absolute bottom-4 left-1/2 -translate-x-1/2 flex items-center gap-4 bg-black/50 backdrop-blur-sm rounded-lg px-4 py-2 text-white",children:[e.jsx("span",{className:"text-sm",children:r?.fileName}),e.jsxs(T,{variant:"outline",className:"border-white/30 text-white",children:[i+1," / ",c.length]}),r?.fileSize&&e.jsx("span",{className:"text-sm opacity-70",children:u(r.fileSize)})]})]})})}),e.jsx(U,{open:j,onOpenChange:p,children:e.jsxs($,{className:"max-w-6xl max-h-[95vh]",children:[e.jsx(ee,{children:e.jsxs(se,{className:"flex items-center justify-between",children:[e.jsx("span",{children:r?.fileName}),e.jsxs("div",{className:"flex gap-2",children:[e.jsxs(w,{variant:"outline",size:"sm",onClick:()=>window.open(r?.url||"","_blank"),children:[" ",e.jsx(J,{className:"h-4 w-4 mr-2"})," Скачать "]}),e.jsxs(w,{variant:"outline",size:"sm",onClick:()=>window.print(),children:[" ",e.jsx(L,{className:"h-4 w-4 mr-2"})," Печать "]})]})]})}),e.jsx("div",{className:"w-full h-[70vh] border rounded-lg overflow-hidden",children:e.jsx("iframe",{src:r?.url,className:"w-full h-full",title:r?.fileName||"pdf"})})]})})]})}function ys(){return e.jsx(Ee,{children:e.jsx(as,{})})}function as(){const[s,a]=l.useState(null);l.useEffect(()=>{const t=i=>{a(i.detail)};return window.addEventListener("openItemDetails",t),()=>window.removeEventListener("openItemDetails",t)},[]);const o=()=>{a(null)};return s?e.jsx(U,{open:!!s,onOpenChange:o,children:e.jsxs($,{className:"max-w-6xl max-h-[90vh] overflow-y-auto bg-card border-border/40",children:[e.jsxs(ee,{children:[e.jsxs(se,{className:"text-xl font-bold flex items-center justify-between",children:[e.jsx("span",{className:"font-mono",children:s.catalogItem.sku}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(T,{variant:"outline",children:s.catalogItem.brand.name}),e.jsx(Z,{status:s.accuracy})]})]}),e.jsx("p",{className:"text-muted-foreground leading-relaxed",children:s.catalogItem.description})]}),e.jsxs(ss,{defaultValue:"overview",className:"mt-4",children:[e.jsxs(ts,{className:"grid w-full grid-cols-5 bg-muted/50",children:[e.jsx(M,{value:"overview",children:"Обзор"}),e.jsx(M,{value:"media",children:"Медиа"}),e.jsx(M,{value:"part",children:"Эталонная группа"}),e.jsx(M,{value:"attributes",children:"Атрибуты"}),e.jsx(M,{value:"applicability",children:"Сопоставление"})]}),e.jsxs(P,{value:"overview",className:"space-y-4 mt-4",children:[e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"font-semibold mb-3",children:"Изображения и документы"}),e.jsx(V,{mediaAssets:s.catalogItem.mediaAssets,primaryImage:s.catalogItem.image??null,title:s.catalogItem.sku})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs(A,{variant:"glass",children:[e.jsx(R,{children:e.jsx(E,{className:"text-sm",children:"Информация о товаре"})}),e.jsx(_,{className:"space-y-3",children:e.jsxs("div",{className:"grid grid-cols-2 gap-3",children:[e.jsxs("div",{children:[e.jsx("span",{className:"text-xs text-muted-foreground",children:"Артикул"}),e.jsx("div",{className:"font-mono font-semibold text-sm",children:s.catalogItem.sku})]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-xs text-muted-foreground",children:"Бренд"}),e.jsx("div",{className:"font-semibold text-sm",children:s.catalogItem.brand.name})]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-xs text-muted-foreground",children:"Страна"}),e.jsx("div",{className:"font-semibold text-sm",children:s.catalogItem.brand.country||"Не указана"})]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-xs text-muted-foreground",children:"Тип"}),e.jsx("div",{className:"font-semibold text-sm",children:s.catalogItem.brand.isOem?"OEM":"Aftermarket"})]})]})})]}),e.jsxs(A,{variant:"glass",children:[e.jsx(R,{children:e.jsx(E,{className:"text-sm",children:"Эталонная группа"})}),e.jsxs(_,{className:"space-y-2",children:[e.jsxs("div",{children:[e.jsx("span",{className:"text-xs text-muted-foreground",children:"Название группы"}),e.jsx("div",{className:"font-semibold text-sm",children:s.part.name||"Без названия"})]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-xs text-muted-foreground",children:"Категория"}),e.jsx("div",{className:"font-semibold text-sm",children:s.part.partCategory.name})]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-xs text-muted-foreground",children:"Путь в иерархии"}),e.jsx("div",{className:"font-mono text-xs",children:s.part.path})]})]})]})]})]}),s.notes&&e.jsxs(A,{variant:"glass",className:"border-info/20 bg-info/5",children:[e.jsx(R,{children:e.jsxs(E,{className:"text-info flex items-center gap-2 text-sm",children:[e.jsx("div",{className:"h-2 w-2 rounded-full bg-info"}),"Примечания к сопоставлению"]})}),e.jsx(_,{children:e.jsx("p",{className:"leading-relaxed text-sm",children:s.notes})})]})]}),e.jsx(P,{value:"media",className:"space-y-4 mt-4",children:e.jsx(V,{mediaAssets:s.catalogItem.mediaAssets,primaryImage:s.catalogItem.image??null,title:s.catalogItem.sku})}),e.jsx(P,{value:"part",className:"space-y-4 mt-4",children:e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"font-semibold mb-3 text-sm",children:"Медиа эталонной группы"}),e.jsx(V,{mediaAssets:s.part.mediaAssets,primaryImage:s.part.image??null,title:s.part.name||"Эталонная группа"})]}),e.jsxs(A,{variant:"glass",children:[e.jsx(R,{children:e.jsx(E,{className:"text-sm",children:"Эталонные атрибуты группы"})}),e.jsx(_,{children:e.jsx("div",{className:"space-y-2",children:s.part.attributes.map(t=>e.jsxs("div",{className:"flex items-center justify-between p-2 rounded bg-card border border-border-strong hover:border-primary/40 transition-colors",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"font-medium text-sm",children:t.template.title}),t.template.unit&&e.jsx(T,{variant:"outline",className:"text-xs h-4",children:t.template.unit})]}),e.jsx("div",{className:"font-mono font-bold text-sm",children:t.value})]},t.id))})})]})]})}),e.jsx(P,{value:"attributes",className:"space-y-4 mt-4",children:e.jsxs(A,{variant:"glass",children:[e.jsx(R,{children:e.jsx(E,{className:"text-sm",children:"Атрибуты каталожной позиции"})}),e.jsx(_,{children:e.jsx("div",{className:"space-y-2",children:s.catalogItem.attributes.map(t=>e.jsxs("div",{className:"flex items-center justify-between p-2 rounded bg-card border border-border-strong hover:border-primary/40 transition-colors",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"font-medium text-sm",children:t.template.title}),t.template.unit&&e.jsx(T,{variant:"outline",className:"text-xs h-4",children:t.template.unit})]}),e.jsx("div",{className:"font-mono font-bold text-sm",children:t.value})]},t.id))})})]})}),e.jsx(P,{value:"applicability",className:"space-y-4 mt-4",children:e.jsxs(A,{variant:"glass",children:[e.jsx(R,{children:e.jsx(E,{className:"text-sm",children:"Детали сопоставления"})}),e.jsxs(_,{className:"space-y-3",children:[e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("span",{className:"text-xs text-muted-foreground",children:"Точность совпадения"}),e.jsx("div",{className:"mt-1",children:e.jsx(Z,{status:s.accuracy})})]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-xs text-muted-foreground",children:"ID сопоставления"}),e.jsxs("div",{className:"font-mono font-semibold text-sm",children:["#",s.id]})]})]}),s.notes&&e.jsxs("div",{className:"p-3 rounded bg-info/10 border border-info/20",children:[e.jsx("h4",{className:"font-medium text-sm mb-2",children:"Примечания:"}),e.jsx("p",{className:"text-sm leading-relaxed",children:s.notes})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-4 pt-3 border-t border-border",children:[e.jsxs("div",{children:[e.jsx("span",{className:"text-xs text-muted-foreground",children:"Создано"}),e.jsx("div",{className:"text-sm",children:new Date(s.part.createdAt).toLocaleString("ru-RU")})]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-xs text-muted-foreground",children:"Обновлено"}),e.jsx("div",{className:"text-sm",children:new Date(s.part.updatedAt).toLocaleString("ru-RU")})]})]})]})]})})]})]})}):null}export{ys as default};
