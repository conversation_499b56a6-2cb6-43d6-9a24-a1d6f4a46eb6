---
import MainLayout from "../../../layouts/MainLayout.astro";
import { trpcClient } from "@/lib/trpc";
import { TrpcProvider } from "@/components/providers/TrpcProvider";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

const { id } = Astro.params;
if (!id) return Astro.redirect('/catalog');

let item: any = null;

try {
  item = await trpcClient.crud.catalogItem.findUnique.query({
    where: { id: Number(id) },
    include: {
      brand: true,
      image: true,
      attributes: { include: { template: true } },
      mediaAssets: true,
      applicabilities: {
        include: {
          part: {
            include: {
              partCategory: true,
              attributes: { include: { template: true } }
            }
          }
        }
      }
    }
  });
  if (!item) return Astro.redirect('/catalog');
} catch (error) {
  console.error('Error loading catalog item:', error);
  return Astro.redirect('/catalog');
}
---

<MainLayout title={`Артикул ${item.sku}`} description={item.description || `Каталожная позиция ${item.sku}`}>
  <TrpcProvider client:load>
    <div class="container mx-auto px-4 py-8">
      <!-- Навигация -->
      <nav class="flex mb-8" aria-label="Breadcrumb">
        <ol class="inline-flex items-center space-x-1 md:space-x-3">
          <li class="inline-flex items-center">
            <a href="/" class="inline-flex items-center text-sm font-medium text-muted-foreground hover:text-foreground">
              Главная
            </a>
          </li>
          <li>
            <div class="flex items-center">
              <span class="mx-2 text-muted-foreground">/</span>
              <a href="/catalog" class="text-sm font-medium text-muted-foreground hover:text-foreground">Каталог</a>
            </div>
          </li>
          <li aria-current="page">
            <div class="flex items-center">
              <span class="mx-2 text-muted-foreground">/</span>
              <span class="text-sm font-medium text-foreground">{item.sku}</span>
            </div>
          </li>
        </ol>
      </nav>

      <!-- Заголовок -->
      <div class="mb-8">
        <div class="flex items-start justify-between">
          <div>
            <h1 class="text-3xl font-bold tracking-tight mb-2">{item.sku}</h1>
            <div class="flex items-center gap-4 mb-4">
              <Badge variant="secondary">{item.brand?.name}</Badge>
              {item.isPublic ? (
                <span class="text-xs rounded bg-green-500/10 text-green-700 dark:text-green-400 px-2 py-0.5">Публичная</span>
              ) : (
                <span class="text-xs rounded bg-amber-500/10 text-amber-700 dark:text-amber-400 px-2 py-0.5">Приватная</span>
              )}
            </div>
            {item.description && (
              <p class="text-muted-foreground max-w-2xl">{item.description}</p>
            )}
          </div>
        </div>
      </div>

      <!-- Привязанные группы (Part) -->
      {item.applicabilities?.length > 0 && (
        <div class="mb-12">
          <h2 class="text-2xl font-semibold mb-6">Группы взаимозаменяемости</h2>
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {item.applicabilities.map((ap: any) => (
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">{ap.part.name || `Запчасть #${ap.part.id}`}</CardTitle>
                </CardHeader>
                <CardContent>
                  <div class="text-sm text-muted-foreground mb-3">{ap.part.partCategory?.name}</div>
                  {ap.notes && (
                    <div class="text-xs text-muted-foreground mb-2">{ap.notes}</div>
                  )}
                  {ap.part.attributes?.length > 0 && (
                    <div class="space-y-1">
                      {ap.part.attributes.slice(0,3).map((attr: any) => (
                        <div class="flex justify-between text-sm">
                          <span class="text-muted-foreground">{attr.template.title}:</span>
                          <span class="font-medium">{attr.value}</span>
                        </div>
                      ))}
                    </div>
                  )}
                  <div class="mt-4">
                    <a href={`/catalog/parts/${ap.part.id}`} class="inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 border border-input bg-background shadow-xs hover:bg-accent hover:text-accent-foreground h-9 px-4 py-2">Открыть</a>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )}

      <!-- Атрибуты (первые N) -->
      {item.attributes?.length > 0 && (
        <div class="mb-12">
          <h2 class="text-2xl font-semibold mb-6">Характеристики</h2>
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {item.attributes.map((attr: any) => (
              <div class="p-4 bg-muted/20 rounded-lg">
                <div class="text-xs text-muted-foreground">{attr.template.title}</div>
                <div class="text-sm font-medium">{attr.value}</div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  </TrpcProvider>
</MainLayout>

