import { e as createComponent, f as createAstro, k as renderComponent, r as renderTemplate, m as maybeRenderHead, h as addAttribute } from '../chunks/astro/server_D7mwM5eH.mjs';
import 'kleur/colors';
import { $ as $$MainLayout } from '../chunks/MainLayout_DqiWx9vO.mjs';
import { t as trpcClient } from '../chunks/clients_B0mKroud.mjs';
import { T as TrpcProvider } from '../chunks/TrpcProvider_BEGJmLJs.mjs';
import { C as Card, b as CardHeader, c as CardTitle, d as CardDescription, a as CardContent } from '../chunks/card_DVAsqpYZ.mjs';
export { renderers } from '../renderers.mjs';

const $$Astro = createAstro();
const $$Equipment = createComponent(async ($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro, $$props, $$slots);
  Astro2.self = $$Equipment;
  const brandIdParam = Astro2.url.searchParams.get("brand");
  const brandId = brandIdParam ? Number(brandIdParam) : void 0;
  let models = [];
  let brand = null;
  try {
    if (brandId) {
      brand = await trpcClient.crud.brand.findUnique.query({ where: { id: brandId } });
    }
    models = await trpcClient.crud.equipmentModel.findMany.query({
      where: brandId ? { brandId } : void 0,
      include: {
        brand: true,
        attributes: { include: { template: true }, take: 3 }
      },
      take: 40,
      orderBy: { name: "asc" }
    }) || [];
  } catch (error) {
    console.error("Error loading equipment models:", error);
  }
  return renderTemplate`${renderComponent($$result, "MainLayout", $$MainLayout, { "title": "\u041C\u043E\u0434\u0435\u043B\u0438 \u0442\u0435\u0445\u043D\u0438\u043A\u0438", "description": "\u041A\u0430\u0442\u0430\u043B\u043E\u0433 \u043C\u043E\u0434\u0435\u043B\u0435\u0439 \u0442\u0435\u0445\u043D\u0438\u043A\u0438 \u0438 \u0438\u0445 \u043F\u0440\u0438\u043C\u0435\u043D\u0438\u043C\u043E\u0441\u0442\u044C \u0441 \u0437\u0430\u043F\u0447\u0430\u0441\u0442\u044F\u043C\u0438" }, { "default": async ($$result2) => renderTemplate` ${renderComponent($$result2, "TrpcProvider", TrpcProvider, { "client:load": true, "client:component-hydration": "load", "client:component-path": "@/components/providers/TrpcProvider", "client:component-export": "TrpcProvider" }, { "default": async ($$result3) => renderTemplate` ${maybeRenderHead()}<div class="container mx-auto px-4 py-8"> <!-- Заголовок --> <div class="mb-8"> <h1 class="text-3xl font-bold tracking-tight mb-2"> ${brand ? `\u041C\u043E\u0434\u0435\u043B\u0438 \u0442\u0435\u0445\u043D\u0438\u043A\u0438: ${brand.name}` : "\u041C\u043E\u0434\u0435\u043B\u0438 \u0442\u0435\u0445\u043D\u0438\u043A\u0438"} </h1> ${brand ? renderTemplate`<p class="text-muted-foreground">Показываются модели производителя ${brand.name}</p>` : renderTemplate`<p class="text-muted-foreground">Список всех моделей техники c основными атрибутами</p>`} </div> ${models.length > 0 ? renderTemplate`<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"> ${models.map((model) => renderTemplate`${renderComponent($$result3, "Card", Card, {}, { "default": async ($$result4) => renderTemplate` ${renderComponent($$result4, "CardHeader", CardHeader, {}, { "default": async ($$result5) => renderTemplate` ${renderComponent($$result5, "CardTitle", CardTitle, { "className": "text-lg" }, { "default": async ($$result6) => renderTemplate`${model.name}` })} ${model.brand && renderTemplate`${renderComponent($$result5, "CardDescription", CardDescription, {}, { "default": async ($$result6) => renderTemplate`${model.brand.name}` })}`}` })} ${renderComponent($$result4, "CardContent", CardContent, {}, { "default": async ($$result5) => renderTemplate`${model.attributes.length > 0 && renderTemplate`<div class="space-y-1"> ${model.attributes.map((attr) => renderTemplate`<div class="flex justify-between text-sm"> <span class="text-muted-foreground">${attr.template.title}:</span> <span class="font-medium">${attr.value}</span> </div>`)} </div>`}<div class="mt-4"> <a${addAttribute(`/equipment/${model.id}`, "href")} class="inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 border border-input bg-background shadow-xs hover:bg-accent hover:text-accent-foreground h-9 px-4 py-2">
Открыть
</a> </div> ` })} ` })}`)} </div>` : renderTemplate`<div class="text-center py-12"> <h3 class="text-lg font-semibold mb-2">Модели не найдены</h3> <p class="text-muted-foreground">Не удалось загрузить список моделей или он пуст</p> </div>`} </div> ` })} ` })}`;
}, "D:/Dev/parttec/site/src/pages/equipment.astro", void 0);

const $$file = "D:/Dev/parttec/site/src/pages/equipment.astro";
const $$url = "/equipment";

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  default: $$Equipment,
  file: $$file,
  url: $$url
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };
