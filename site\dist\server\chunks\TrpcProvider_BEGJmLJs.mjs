import { jsx } from 'react/jsx-runtime';
import { QueryClientProvider } from '@tanstack/react-query';
import { a as trpc, q as queryClient, b as trpcClient } from './clients_B0mKroud.mjs';

function TrpcProvider({ children }) {
  return /* @__PURE__ */ jsx(trpc.Provider, { client: trpcClient, queryClient, children: /* @__PURE__ */ jsx(QueryClientProvider, { client: queryClient, children }) });
}

export { TrpcProvider as T };
