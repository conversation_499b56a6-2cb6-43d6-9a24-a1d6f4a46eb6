import{j as r}from"./jsx-runtime.D_zvdyIk.js";import{r as c}from"./index.0yr9KlQE.js";import{S as u}from"./index.BWNh2K4G.js";import{c as m}from"./index.3rXK4OIH.js";import{c as p}from"./utils.CBfrqCZ4.js";import{t as f,q as t,a as l,Q as g}from"./clients.CgSQcUWs.js";const v=m("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-sm hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline",gradient:"gradient-primary text-primary-foreground shadow-sm hover:opacity-90"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),h=c.forwardRef(({className:e,variant:o,size:n,asChild:s=!1,...i},a)=>{const d=s?u:"button";return r.jsx(d,{className:p(v({variant:o,size:n,className:e})),ref:a,...i})});h.displayName="ModernButton";function C({children:e}){return r.jsx(f.Provider,{client:l,queryClient:t,children:r.jsx(g,{client:t,children:e})})}export{h as M,C as T};
