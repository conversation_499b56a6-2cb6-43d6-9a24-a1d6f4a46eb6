import{r as o}from"./index.0yr9KlQE.js";import{t as F}from"./clients.CgSQcUWs.js";class m{static STORAGE_KEY="catalog-search-filters";static EVENT_NAME="catalog-filters-changed";static getFilters(){if(typeof window>"u")return this.getDefaultFilters();try{const s=localStorage.getItem(this.STORAGE_KEY);if(s)return{...this.getDefaultFilters(),...JSON.parse(s)}}catch(s){console.warn("Failed to parse stored catalog filters:",s)}return this.getDefaultFilters()}static setFilters(s){if(!(typeof window>"u"))try{localStorage.setItem(this.STORAGE_KEY,JSON.stringify(s)),window.dispatchEvent(new CustomEvent(this.EVENT_NAME,{detail:s}))}catch(i){console.warn("Failed to store catalog filters:",i)}}static updateFilters(s){const i=this.getFilters();this.setFilters({...i,...s})}static clearFilters(){this.setFilters(this.getDefaultFilters())}static subscribe(s){if(typeof window>"u")return()=>{};const i=u=>{s(u.detail)};return window.addEventListener(this.EVENT_NAME,i),()=>{window.removeEventListener(this.EVENT_NAME,i)}}static getDefaultFilters(){return{query:"",categoryIds:[],brandIds:[],attributeFilters:{},accuracyLevels:[],isOemOnly:!1}}}function E(){const[a,s]=o.useState(()=>m.getFilters());return o.useEffect(()=>m.subscribe(s),[]),{filters:a,setFilters:r=>{m.setFilters(r)},updateFilters:r=>{m.updateFilters(r)},clearFilters:()=>{m.clearFilters()}}}function y(a,s){const[i,u]=o.useState(a);return o.useEffect(()=>{const f=setTimeout(()=>{u(a)},s);return()=>{clearTimeout(f)}},[a,s]),i}function w(){const{filters:a,setFilters:s,updateFilters:i,clearFilters:u}=E(),f=y(a.query,300),{data:r,isLoading:g,error:h}=F.site.search.catalogItems.useQuery({search:f||void 0,brandIds:a.brandIds,categoryIds:a.categoryIds,isOemOnly:a.isOemOnly,accuracy:a.accuracyLevels||void 0,attributeFilters:Object.entries(a.attributeFilters||{}).map(([l,e])=>({templateId:Number(l),values:e.values?.length?e.values:void 0,minValue:e.numericRange?.[0],maxValue:e.numericRange?.[1]})),limit:50,offset:0,sortBy:"updatedAt",sortDir:"desc"},{staleTime:3e4,gcTime:5*6e4});typeof window<"u"&&(console.log("Search filters:",a),console.log("Search result:",r),console.log("Search error:",h),console.log("Is loading:",g));const d=o.useMemo(()=>{if(!r?.items)return[];const l=[];for(const e of r.items)if(e.applicabilities)for(const n of e.applicabilities)l.push({id:n.id,partId:n.partId,catalogItemId:n.catalogItemId,accuracy:n.accuracy,notes:n.notes,part:n.part,catalogItem:{id:e.id,sku:e.sku,description:e.description,brandId:e.brandId,brand:e.brand,isPublic:!0,attributes:e.attributes,image:e.image??null,mediaAssets:e.mediaAssets??[]}});return l},[r]),b=o.useMemo(()=>{const l={},e={};return d.forEach(n=>{n.catalogItem.attributes.forEach(t=>{t.template.dataType==="NUMBER"&&t.numericValue!==void 0&&t.numericValue!==null?e[t.templateId]?(e[t.templateId].min=Math.min(e[t.templateId].min,t.numericValue),e[t.templateId].max=Math.max(e[t.templateId].max,t.numericValue)):e[t.templateId]={min:t.numericValue,max:t.numericValue,avg:0}:(l[t.templateId]||(l[t.templateId]=[]),l[t.templateId].includes(t.value)||l[t.templateId].push(t.value))})}),Object.keys(e).forEach(n=>{const t=Number.parseInt(n),p=d.flatMap(c=>c.catalogItem.attributes).filter(c=>c.templateId===t&&c.numericValue!==void 0&&c.numericValue!==null).map(c=>c.numericValue);p.length>0&&(e[t].avg=p.reduce((c,I)=>c+I,0)/p.length)}),{values:l,numericStats:e}},[d]);return{filters:a,setFilters:s,updateFilters:i,clearFilters:u,results:d,totalCount:r?.total||0,filteredCount:d.length,isLoading:g,availableAttributeValues:b}}export{w as u};
