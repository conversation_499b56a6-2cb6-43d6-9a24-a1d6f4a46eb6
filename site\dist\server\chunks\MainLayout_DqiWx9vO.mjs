import { e as createComponent, f as createAstro, h as addAttribute, l as renderScript, r as renderTemplate, k as renderComponent, n as renderHead, o as renderSlot } from './astro/server_D7mwM5eH.mjs';
import 'kleur/colors';
/* empty css                           */
import { jsx, jsxs } from 'react/jsx-runtime';
import 'react';
import { a as authClient } from './auth-client_BsqqHDQx.mjs';
import { Slot } from '@radix-ui/react-slot';
import { cva } from 'class-variance-authority';
import { clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';

function cn(...inputs) {
  return twMerge(clsx(inputs));
}

const buttonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",
  {
    variants: {
      variant: {
        default: "bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",
        destructive: "bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",
        outline: "border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",
        secondary: "bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",
        ghost: "hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",
        link: "text-primary underline-offset-4 hover:underline"
      },
      size: {
        default: "h-9 px-4 py-2 has-[>svg]:px-3",
        sm: "h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",
        lg: "h-10 rounded-md px-6 has-[>svg]:px-4",
        icon: "size-9"
      }
    },
    defaultVariants: {
      variant: "default",
      size: "default"
    }
  }
);
function Button({
  className,
  variant,
  size,
  asChild = false,
  ...props
}) {
  const Comp = asChild ? Slot : "button";
  return /* @__PURE__ */ jsx(
    Comp,
    {
      "data-slot": "button",
      className: cn(buttonVariants({ variant, size, className })),
      ...props
    }
  );
}

function AuthNav() {
  const { data: session, isPending } = authClient.useSession();
  if (isPending) return null;
  if (!session) {
    return /* @__PURE__ */ jsxs("div", { className: "flex items-center gap-2", children: [
      /* @__PURE__ */ jsx("a", { href: "/login", className: "text-sm text-foreground/80 hover:underline", children: "Вход" }),
      /* @__PURE__ */ jsx("a", { href: "/register", className: "text-sm text-foreground/80 hover:underline", children: "Регистрация" })
    ] });
  }
  return /* @__PURE__ */ jsxs("div", { className: "flex items-center gap-2", children: [
    /* @__PURE__ */ jsx("a", { href: "/account", className: "text-sm text-foreground/80 hover:underline", children: "Кабинет" }),
    /* @__PURE__ */ jsx(
      Button,
      {
        size: "sm",
        variant: "outline",
        onClick: async () => {
          await authClient.signOut({
            fetchOptions: { onSuccess: () => window.location.href = "/" }
          });
        },
        children: "Выйти"
      }
    )
  ] });
}

const $$Astro$1 = createAstro();
const $$ClientRouter = createComponent(($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro$1, $$props, $$slots);
  Astro2.self = $$ClientRouter;
  const { fallback = "animate" } = Astro2.props;
  return renderTemplate`<meta name="astro-view-transitions-enabled" content="true"><meta name="astro-view-transitions-fallback"${addAttribute(fallback, "content")}>${renderScript($$result, "D:/Dev/parttec/site/node_modules/astro/components/ClientRouter.astro?astro&type=script&index=0&lang.ts")}`;
}, "D:/Dev/parttec/site/node_modules/astro/components/ClientRouter.astro", void 0);

const $$Astro = createAstro();
const $$MainLayout = createComponent(($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro, $$props, $$slots);
  Astro2.self = $$MainLayout;
  const { title, description = "PartTec3 - \u041A\u0430\u0442\u0430\u043B\u043E\u0433 \u0432\u0437\u0430\u0438\u043C\u043E\u0437\u0430\u043C\u0435\u043D\u044F\u0435\u043C\u044B\u0445 \u0437\u0430\u043F\u0447\u0430\u0441\u0442\u0435\u0439" } = Astro2.props;
  return renderTemplate`<html lang="ru"> <head>${renderComponent($$result, "ClientRouter", $$ClientRouter, {})}<meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1"><meta name="description"${addAttribute(description, "content")}><title>${title} | PartTec3</title><meta name="view-transition" content="same-origin">${renderHead()}</head> <body class="min-h-screen bg-background font-sans antialiased"> <header class="sticky top-0 z-50 w-full border-b border-border/40 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60"> <div class="container flex h-14 max-w-screen-2xl items-center"> <div class="mr-4 hidden md:flex"> <a href="/" class="mr-6 flex items-center space-x-2"> <span class="hidden font-bold sm:inline-block">PartTec3</span> </a> <nav class="flex items-center gap-4 text-sm lg:gap-6"> <a href="/" class="transition-colors hover:text-foreground/80 text-foreground/60">
Главная
</a> <a href="/catalog" class="transition-colors hover:text-foreground/80 text-foreground/60">
Каталог
</a> <a href="/categories" class="transition-colors hover:text-foreground/80 text-foreground/60">
Категории
</a> <a href="/brands" class="transition-colors hover:text-foreground/80 text-foreground/60">
Бренды
</a> <a href="/equipment" class="transition-colors hover:text-foreground/80 text-foreground/60">
Техника
</a> <a href="/search" class="transition-colors hover:text-foreground/80 text-foreground/60">
Поиск
</a> </nav> </div> <div class="ml-auto"> <!-- Кнопки входа/выхода --> ${renderComponent($$result, "AuthNav", AuthNav, { "client:load": true, "client:component-hydration": "load", "client:component-path": "@/components/navigation/AuthNav", "client:component-export": "AuthNav" })} </div> </div> </header> <main class="flex-1"> ${renderSlot($$result, $$slots["default"])} </main> <footer class="border-t py-6 md:py-0"> <div class="container flex flex-col items-center justify-between gap-4 md:h-24 md:flex-row"> <div class="flex flex-col items-center gap-4 px-8 md:flex-row md:gap-2 md:px-0"> <p class="text-center text-sm leading-loose text-muted-foreground md:text-left">
© 2024 PartTec3. Каталог взаимозаменяемых запчастей.
</p> </div> </div> </footer> </body></html>`;
}, "D:/Dev/parttec/site/src/layouts/MainLayout.astro", void 0);

export { $$MainLayout as $, Button as B, cn as c };
