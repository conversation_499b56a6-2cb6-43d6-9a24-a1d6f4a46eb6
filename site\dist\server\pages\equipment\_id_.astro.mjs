import { e as createComponent, f as createAstro, k as renderComponent, r as renderTemplate, m as maybeRenderHead, h as addAttribute } from '../../chunks/astro/server_D7mwM5eH.mjs';
import 'kleur/colors';
import { $ as $$MainLayout } from '../../chunks/MainLayout_DqiWx9vO.mjs';
import { t as trpcClient } from '../../chunks/clients_B0mKroud.mjs';
import { T as TrpcProvider } from '../../chunks/TrpcProvider_BEGJmLJs.mjs';
import { C as Card, b as CardHeader, c as CardTitle, d as CardDescription, a as CardContent } from '../../chunks/card_DVAsqpYZ.mjs';
import { B as Badge } from '../../chunks/badge_B5LLCYzT.mjs';
export { renderers } from '../../renderers.mjs';

const $$Astro = createAstro();
const $$id = createComponent(async ($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro, $$props, $$slots);
  Astro2.self = $$id;
  const { id } = Astro2.params;
  if (!id) {
    return Astro2.redirect("/equipment");
  }
  let model = null;
  let parts = [];
  try {
    model = await trpcClient.crud.equipmentModel.findUnique.query({
      where: { id },
      include: {
        brand: true,
        attributes: { include: { template: true } }
      }
    });
    if (!model) return Astro2.redirect("/equipment");
    const equipmentApplicabilities = await trpcClient.crud.equipmentApplicability.findMany.query({
      where: { equipmentModelId: id },
      include: {
        part: {
          include: {
            partCategory: true,
            attributes: { include: { template: true } }
          }
        }
      },
      take: 60,
      orderBy: { id: "desc" }
    });
    parts = (equipmentApplicabilities || []).map((ea) => ea.part);
  } catch (error) {
    console.error("Error loading equipment model details:", error);
    return Astro2.redirect("/equipment");
  }
  return renderTemplate`${renderComponent($$result, "MainLayout", $$MainLayout, { "title": model.name, "description": `\u041C\u043E\u0434\u0435\u043B\u044C \u0442\u0435\u0445\u043D\u0438\u043A\u0438 ${model.name}` }, { "default": async ($$result2) => renderTemplate` ${renderComponent($$result2, "TrpcProvider", TrpcProvider, { "client:load": true, "client:component-hydration": "load", "client:component-path": "@/components/providers/TrpcProvider", "client:component-export": "TrpcProvider" }, { "default": async ($$result3) => renderTemplate` ${maybeRenderHead()}<div class="container mx-auto px-4 py-8"> <!-- Хлебные крошки --> <nav class="flex mb-8" aria-label="Breadcrumb"> <ol class="inline-flex items-center space-x-1 md:space-x-3"> <li class="inline-flex items-center"> <a href="/" class="inline-flex items-center text-sm font-medium text-muted-foreground hover:text-foreground">
Главная
</a> </li> <li> <div class="flex items-center"> <span class="mx-2 text-muted-foreground">/</span> <a href="/equipment" class="text-sm font-medium text-muted-foreground hover:text-foreground">Модели техники</a> </div> </li> <li aria-current="page"> <div class="flex items-center"> <span class="mx-2 text-muted-foreground">/</span> <span class="text-sm font-medium text-foreground">${model.name}</span> </div> </li> </ol> </nav> <!-- Заголовок --> <div class="mb-8"> <div class="flex items-start justify-between"> <div> <h1 class="text-3xl font-bold tracking-tight mb-2">${model.name}</h1> ${model.brand && renderTemplate`<div class="flex items-center gap-4 mb-4"> ${renderComponent($$result3, "Badge", Badge, { "variant": "secondary" }, { "default": async ($$result4) => renderTemplate`${model.brand.name}` })} </div>`} </div> </div> <!-- Атрибуты модели --> ${model.attributes.length > 0 && renderTemplate`<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"> ${model.attributes.map((attr) => renderTemplate`<div class="p-4 bg-muted/20 rounded-lg"> <div class="text-xs text-muted-foreground">${attr.template.title}</div> <div class="text-sm font-medium">${attr.value}</div> </div>`)} </div>`} </div> <!-- Применимые запчасти --> <div> <div class="flex items-center justify-between mb-6"> <h2 class="text-2xl font-semibold">Применимые запчасти</h2> </div> ${parts.length > 0 ? renderTemplate`<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"> ${parts.map((part) => renderTemplate`${renderComponent($$result3, "Card", Card, {}, { "default": async ($$result4) => renderTemplate` ${renderComponent($$result4, "CardHeader", CardHeader, {}, { "default": async ($$result5) => renderTemplate` ${renderComponent($$result5, "CardTitle", CardTitle, { "className": "text-lg" }, { "default": async ($$result6) => renderTemplate`${part.name || `\u0417\u0430\u043F\u0447\u0430\u0441\u0442\u044C #${part.id}`}` })} ${renderComponent($$result5, "CardDescription", CardDescription, {}, { "default": async ($$result6) => renderTemplate`${part.partCategory?.name}` })} ` })} ${renderComponent($$result4, "CardContent", CardContent, {}, { "default": async ($$result5) => renderTemplate`${part.attributes?.length > 0 && renderTemplate`<div class="space-y-1"> ${part.attributes.slice(0, 3).map((attr) => renderTemplate`<div class="flex justify-between text-sm"> <span class="text-muted-foreground">${attr.template.title}:</span> <span class="font-medium">${attr.value}</span> </div>`)} </div>`}<div class="mt-4"> <a${addAttribute(`/catalog/parts/${part.id}`, "href")} class="inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 border border-input bg-background shadow-xs hover:bg-accent hover:text-accent-foreground h-9 px-4 py-2">
Открыть
</a> </div> ` })} ` })}`)} </div>` : renderTemplate`<div class="text-center py-12"> <h3 class="text-lg font-semibold mb-2">Запчасти не найдены</h3> <p class="text-muted-foreground">Для этой модели техники ещё нет привязанных запчастей</p> </div>`} </div> </div> ` })} ` })}`;
}, "D:/Dev/parttec/site/src/pages/equipment/[id].astro", void 0);

const $$file = "D:/Dev/parttec/site/src/pages/equipment/[id].astro";
const $$url = "/equipment/[id]";

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  default: $$id,
  file: $$file,
  url: $$url
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };
