import { QueryClient } from '@tanstack/react-query';
import { createTRPCReact } from '@trpc/react-query';
import { createTRPCProxyClient, loggerLink, httpBatchLink } from '@trpc/client';
import superjson from 'superjson';

const baseURL$1 = "http://localhost:3000";
const trpc = createTRPCReact();
const trpcClient$1 = createTRPCProxyClient({
  links: [
    loggerLink({
      enabled: () => false
    }),
    httpBatchLink({
      url: `${baseURL$1}/trpc`,
      transformer: superjson,
      fetch: (url, options) => fetch(url, {
        ...options,
        credentials: "include"
      })
    })
  ]
});

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 6e4,
      gcTime: 30 * 6e4,
      retry: 2,
      refetchOnWindowFocus: false,
      refetchOnReconnect: true,
      refetchOnMount: false,
      refetchInterval: false
    },
    mutations: {
      retry: 1
    }
  }
});
const baseURL = "http://localhost:3000";
const trpcClient = trpc.createClient({
  links: [
    loggerLink({ enabled: () => false }),
    httpBatchLink({
      url: `${baseURL}/trpc`,
      transformer: superjson,
      fetch: (url, options) => fetch(url, { ...options, credentials: "include" })
    })
  ]
});

export { trpc as a, trpcClient as b, queryClient as q, trpcClient$1 as t };
