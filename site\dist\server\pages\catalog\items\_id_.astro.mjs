import { e as createComponent, f as createAstro, k as renderComponent, r as renderTemplate, m as maybeRenderHead, h as addAttribute } from '../../../chunks/astro/server_D7mwM5eH.mjs';
import 'kleur/colors';
import { $ as $$MainLayout } from '../../../chunks/MainLayout_DqiWx9vO.mjs';
import { t as trpcClient } from '../../../chunks/clients_B0mKroud.mjs';
import { T as TrpcProvider } from '../../../chunks/TrpcProvider_BEGJmLJs.mjs';
import { C as Card, b as CardHeader, c as CardTitle, a as CardContent } from '../../../chunks/card_DVAsqpYZ.mjs';
import { B as Badge } from '../../../chunks/badge_B5LLCYzT.mjs';
export { renderers } from '../../../renderers.mjs';

const $$Astro = createAstro();
const $$id = createComponent(async ($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro, $$props, $$slots);
  Astro2.self = $$id;
  const { id } = Astro2.params;
  if (!id) return Astro2.redirect("/catalog");
  let item = null;
  try {
    item = await trpcClient.crud.catalogItem.findUnique.query({
      where: { id: Number(id) },
      include: {
        brand: true,
        image: true,
        attributes: { include: { template: true } },
        mediaAssets: true,
        applicabilities: {
          include: {
            part: {
              include: {
                partCategory: true,
                attributes: { include: { template: true } }
              }
            }
          }
        }
      }
    });
    if (!item) return Astro2.redirect("/catalog");
  } catch (error) {
    console.error("Error loading catalog item:", error);
    return Astro2.redirect("/catalog");
  }
  return renderTemplate`${renderComponent($$result, "MainLayout", $$MainLayout, { "title": `\u0410\u0440\u0442\u0438\u043A\u0443\u043B ${item.sku}`, "description": item.description || `\u041A\u0430\u0442\u0430\u043B\u043E\u0436\u043D\u0430\u044F \u043F\u043E\u0437\u0438\u0446\u0438\u044F ${item.sku}` }, { "default": async ($$result2) => renderTemplate` ${renderComponent($$result2, "TrpcProvider", TrpcProvider, { "client:load": true, "client:component-hydration": "load", "client:component-path": "@/components/providers/TrpcProvider", "client:component-export": "TrpcProvider" }, { "default": async ($$result3) => renderTemplate` ${maybeRenderHead()}<div class="container mx-auto px-4 py-8"> <!-- Навигация --> <nav class="flex mb-8" aria-label="Breadcrumb"> <ol class="inline-flex items-center space-x-1 md:space-x-3"> <li class="inline-flex items-center"> <a href="/" class="inline-flex items-center text-sm font-medium text-muted-foreground hover:text-foreground">
Главная
</a> </li> <li> <div class="flex items-center"> <span class="mx-2 text-muted-foreground">/</span> <a href="/catalog" class="text-sm font-medium text-muted-foreground hover:text-foreground">Каталог</a> </div> </li> <li aria-current="page"> <div class="flex items-center"> <span class="mx-2 text-muted-foreground">/</span> <span class="text-sm font-medium text-foreground">${item.sku}</span> </div> </li> </ol> </nav> <!-- Заголовок --> <div class="mb-8"> <div class="flex items-start justify-between"> <div> <h1 class="text-3xl font-bold tracking-tight mb-2">${item.sku}</h1> <div class="flex items-center gap-4 mb-4"> ${renderComponent($$result3, "Badge", Badge, { "variant": "secondary" }, { "default": async ($$result4) => renderTemplate`${item.brand?.name}` })} ${item.isPublic ? renderTemplate`<span class="text-xs rounded bg-green-500/10 text-green-700 dark:text-green-400 px-2 py-0.5">Публичная</span>` : renderTemplate`<span class="text-xs rounded bg-amber-500/10 text-amber-700 dark:text-amber-400 px-2 py-0.5">Приватная</span>`} </div> ${item.description && renderTemplate`<p class="text-muted-foreground max-w-2xl">${item.description}</p>`} </div> </div> </div> <!-- Привязанные группы (Part) --> ${item.applicabilities?.length > 0 && renderTemplate`<div class="mb-12"> <h2 class="text-2xl font-semibold mb-6">Группы взаимозаменяемости</h2> <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"> ${item.applicabilities.map((ap) => renderTemplate`${renderComponent($$result3, "Card", Card, {}, { "default": async ($$result4) => renderTemplate` ${renderComponent($$result4, "CardHeader", CardHeader, {}, { "default": async ($$result5) => renderTemplate` ${renderComponent($$result5, "CardTitle", CardTitle, { "className": "text-lg" }, { "default": async ($$result6) => renderTemplate`${ap.part.name || `\u0417\u0430\u043F\u0447\u0430\u0441\u0442\u044C #${ap.part.id}`}` })} ` })} ${renderComponent($$result4, "CardContent", CardContent, {}, { "default": async ($$result5) => renderTemplate` <div class="text-sm text-muted-foreground mb-3">${ap.part.partCategory?.name}</div> ${ap.notes && renderTemplate`<div class="text-xs text-muted-foreground mb-2">${ap.notes}</div>`}${ap.part.attributes?.length > 0 && renderTemplate`<div class="space-y-1"> ${ap.part.attributes.slice(0, 3).map((attr) => renderTemplate`<div class="flex justify-between text-sm"> <span class="text-muted-foreground">${attr.template.title}:</span> <span class="font-medium">${attr.value}</span> </div>`)} </div>`}<div class="mt-4"> <a${addAttribute(`/catalog/parts/${ap.part.id}`, "href")} class="inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 border border-input bg-background shadow-xs hover:bg-accent hover:text-accent-foreground h-9 px-4 py-2">Открыть</a> </div> ` })} ` })}`)} </div> </div>`} <!-- Атрибуты (первые N) --> ${item.attributes?.length > 0 && renderTemplate`<div class="mb-12"> <h2 class="text-2xl font-semibold mb-6">Характеристики</h2> <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"> ${item.attributes.map((attr) => renderTemplate`<div class="p-4 bg-muted/20 rounded-lg"> <div class="text-xs text-muted-foreground">${attr.template.title}</div> <div class="text-sm font-medium">${attr.value}</div> </div>`)} </div> </div>`} </div> ` })} ` })}`;
}, "D:/Dev/parttec/site/src/pages/catalog/items/[id].astro", void 0);

const $$file = "D:/Dev/parttec/site/src/pages/catalog/items/[id].astro";
const $$url = "/catalog/items/[id]";

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  default: $$id,
  file: $$file,
  url: $$url
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };
