import { e as createComponent, f as createAstro, k as renderComponent, r as renderTemplate, m as maybeRenderHead, h as addAttribute } from '../../../chunks/astro/server_D7mwM5eH.mjs';
import 'kleur/colors';
import { $ as $$MainLayout } from '../../../chunks/MainLayout_DqiWx9vO.mjs';
import { t as trpcClient } from '../../../chunks/clients_B0mKroud.mjs';
import { T as TrpcProvider } from '../../../chunks/TrpcProvider_BEGJmLJs.mjs';
import { B as Badge } from '../../../chunks/badge_B5LLCYzT.mjs';
import { C as Card, b as CardHeader, c as CardTitle, d as CardDescription, a as CardContent } from '../../../chunks/card_DVAsqpYZ.mjs';
export { renderers } from '../../../renderers.mjs';

const $$Astro = createAstro();
const $$slug = createComponent(async ($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro, $$props, $$slots);
  Astro2.self = $$slug;
  const { slug } = Astro2.params;
  if (!slug) {
    return Astro2.redirect("/brands");
  }
  let brand = null;
  let catalogItems = [];
  let equipmentModels = [];
  try {
    const brandResponse = await trpcClient.crud.brand.findUnique.query({
      where: { slug },
      include: {
        _count: {
          select: {
            catalogItems: true,
            equipmentModel: true
          }
        }
      }
    });
    if (!brandResponse) {
      return Astro2.redirect("/brands");
    }
    brand = brandResponse;
    const catalogItemsResponse = await trpcClient.crud.catalogItem.findMany.query({
      where: { brandId: brand.id },
      include: {
        brand: true,
        attributes: {
          include: { template: true },
          take: 3
        },
        applicabilities: {
          include: {
            part: {
              include: { partCategory: true }
            }
          },
          take: 1
        }
      },
      take: 20,
      orderBy: { sku: "asc" }
    });
    catalogItems = catalogItemsResponse || [];
    if (brand.isOem) {
      const equipmentResponse = await trpcClient.crud.equipmentModel.findMany.query({
        where: { brandId: brand.id },
        include: {
          brand: true,
          attributes: {
            include: { template: true },
            take: 3
          }
        },
        take: 20,
        orderBy: { name: "asc" }
      });
      equipmentModels = equipmentResponse || [];
    }
  } catch (error) {
    console.error("Error loading brand data:", error);
    return Astro2.redirect("/brands");
  }
  return renderTemplate`${renderComponent($$result, "MainLayout", $$MainLayout, { "title": brand.name, "description": `\u041A\u0430\u0442\u0430\u043B\u043E\u0433 \u0437\u0430\u043F\u0447\u0430\u0441\u0442\u0435\u0439 \u0438 \u0442\u0435\u0445\u043D\u0438\u043A\u0438 \u0431\u0440\u0435\u043D\u0434\u0430 ${brand.name}` }, { "default": async ($$result2) => renderTemplate` ${renderComponent($$result2, "TrpcProvider", TrpcProvider, { "client:load": true, "client:component-hydration": "load", "client:component-path": "@/components/providers/TrpcProvider", "client:component-export": "TrpcProvider" }, { "default": async ($$result3) => renderTemplate` ${maybeRenderHead()}<div class="container mx-auto px-4 py-8"> <!-- Хлебные крошки --> <nav class="flex mb-8" aria-label="Breadcrumb"> <ol class="inline-flex items-center space-x-1 md:space-x-3"> <li class="inline-flex items-center"> <a href="/" class="inline-flex items-center text-sm font-medium text-muted-foreground hover:text-foreground">
Главная
</a> </li> <li> <div class="flex items-center"> <span class="mx-2 text-muted-foreground">/</span> <a href="/brands" class="text-sm font-medium text-muted-foreground hover:text-foreground">Бренды</a> </div> </li> <li aria-current="page"> <div class="flex items-center"> <span class="mx-2 text-muted-foreground">/</span> <span class="text-sm font-medium text-foreground">${brand.name}</span> </div> </li> </ol> </nav> <!-- Заголовок бренда --> <div class="mb-8"> <div class="flex items-start justify-between"> <div> <h1 class="text-3xl font-bold tracking-tight mb-2">${brand.name}</h1> <div class="flex items-center gap-4 mb-4"> ${renderComponent($$result3, "Badge", Badge, { "variant": brand.isOem ? "default" : "secondary" }, { "default": async ($$result4) => renderTemplate`${brand.isOem ? "OEM \u043F\u0440\u043E\u0438\u0437\u0432\u043E\u0434\u0438\u0442\u0435\u043B\u044C" : "Aftermarket \u043F\u0440\u043E\u0438\u0437\u0432\u043E\u0434\u0438\u0442\u0435\u043B\u044C"}` })} ${brand.country && renderTemplate`<span class="text-muted-foreground">${brand.country}</span>`} </div> </div> </div> <!-- Статистика --> <div class="grid grid-cols-2 md:grid-cols-4 gap-4"> <div class="text-center p-4 bg-muted/20 rounded-lg"> <div class="text-2xl font-bold">${brand._count.catalogItems}</div> <div class="text-sm text-muted-foreground">Запчастей</div> </div> <div class="text-center p-4 bg-muted/20 rounded-lg"> <div class="text-2xl font-bold">${brand._count.equipmentModel}</div> <div class="text-sm text-muted-foreground">Моделей техники</div> </div> </div> </div> <div class="space-y-12"> <!-- Модели техники (для OEM) --> ${brand.isOem && equipmentModels.length > 0 && renderTemplate`<div> <div class="flex items-center justify-between mb-6"> <h2 class="text-2xl font-semibold">Модели техники</h2> ${equipmentModels.length === 20 && renderTemplate`<a${addAttribute(`/equipment?brand=${brand.id}`, "href")} class="inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 border border-input bg-background shadow-xs hover:bg-accent hover:text-accent-foreground h-9 px-4 py-2">
Показать все
</a>`} </div> <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"> ${equipmentModels.map((model) => renderTemplate`${renderComponent($$result3, "Card", Card, {}, { "default": async ($$result4) => renderTemplate` ${renderComponent($$result4, "CardHeader", CardHeader, {}, { "default": async ($$result5) => renderTemplate` ${renderComponent($$result5, "CardTitle", CardTitle, { "className": "text-lg" }, { "default": async ($$result6) => renderTemplate`${model.name}` })} ${renderComponent($$result5, "CardDescription", CardDescription, {}, { "default": async ($$result6) => renderTemplate`${brand.name}` })} ` })} ${renderComponent($$result4, "CardContent", CardContent, {}, { "default": async ($$result5) => renderTemplate`${model.attributes.length > 0 && renderTemplate`<div class="space-y-1"> ${model.attributes.slice(0, 3).map((attr) => renderTemplate`<div class="flex justify-between text-sm"> <span class="text-muted-foreground">${attr.template.title}:</span> <span class="font-medium">${attr.value}</span> </div>`)} </div>`}` })} ` })}`)} </div> </div>`} <!-- Каталожные позиции --> <div> <div class="flex items-center justify-between mb-6"> <h2 class="text-2xl font-semibold">Каталог запчастей</h2> ${catalogItems.length === 20 && renderTemplate`<a${addAttribute(`/catalog?brand=${brand.id}`, "href")} class="inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 border border-input bg-background shadow-xs hover:bg-accent hover:text-accent-foreground h-9 px-4 py-2">
Показать все
</a>`} </div> ${catalogItems.length > 0 ? renderTemplate`<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"> ${catalogItems.map((item) => renderTemplate`${renderComponent($$result3, "Card", Card, {}, { "default": async ($$result4) => renderTemplate` ${renderComponent($$result4, "CardHeader", CardHeader, {}, { "default": async ($$result5) => renderTemplate` ${renderComponent($$result5, "CardTitle", CardTitle, { "className": "text-lg" }, { "default": async ($$result6) => renderTemplate`${item.sku}` })} ${renderComponent($$result5, "CardDescription", CardDescription, {}, { "default": async ($$result6) => renderTemplate`${brand.name}` })} ` })} ${renderComponent($$result4, "CardContent", CardContent, {}, { "default": async ($$result5) => renderTemplate`${item.description && renderTemplate`<p class="text-sm text-muted-foreground mb-3 line-clamp-2">${item.description}</p>`}${item.applicabilities.length > 0 && renderTemplate`<div class="mb-3"> <div class="text-xs text-muted-foreground mb-1">Группа взаимозаменяемости:</div> <a${addAttribute(`/catalog/parts/${item.applicabilities[0].part.id}`, "href")} class="text-sm text-primary hover:underline"> ${item.applicabilities[0].part.name || `\u0417\u0430\u043F\u0447\u0430\u0441\u0442\u044C #${item.applicabilities[0].part.id}`} </a> <div class="text-xs text-muted-foreground"> ${item.applicabilities[0].part.partCategory.name} </div> </div>`}${item.attributes.length > 0 && renderTemplate`<div class="space-y-1"> ${item.attributes.slice(0, 3).map((attr) => renderTemplate`<div class="flex justify-between text-sm"> <span class="text-muted-foreground">${attr.template.title}:</span> <span class="font-medium">${attr.value}</span> </div>`)} </div>`}` })} ` })}`)} </div>` : renderTemplate`<div class="text-center py-12"> <h3 class="text-lg font-semibold mb-2">Запчасти не найдены</h3> <p class="text-muted-foreground">
У этого бренда пока нет запчастей в каталоге
</p> </div>`} </div> </div> </div> ` })} ` })}`;
}, "D:/Dev/parttec/site/src/pages/catalog/brands/[slug].astro", void 0);

const $$file = "D:/Dev/parttec/site/src/pages/catalog/brands/[slug].astro";
const $$url = "/catalog/brands/[slug]";

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  default: $$slug,
  file: $$file,
  url: $$url
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };
